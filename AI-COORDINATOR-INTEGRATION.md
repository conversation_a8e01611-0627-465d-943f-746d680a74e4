# 🧠 AI Coordinator - التكامل مع النظام الموحد

## 🎯 نظرة عامة

تم دمج **AI Coordinator** كطبقة التنسيق الذكي في النظام الموحد. هذا المشروع هو **النقطة المركزية** للتحكم في جميع النماذج والخدمات.

## 🏗️ البنية المحدثة

```
AI Development Assistant (Unified)
├── 🧠 AI Coordinator (Port 3333) ← المشروع الرئيسي
├── 💬 AnythingLLM (Port 3001)
├── 🖥️ Ollama WebUI (Port 3000)
├── 🔄 n8n (Port 5678)
└── 🤖 Ollama API (Port 11434)
```

## 🚀 التشغيل السريع

### 1. تشغيل النظام الكامل:
```powershell
.\start-unified.ps1
```

### 2. تشغيل AI Coordinator فقط:
```powershell
.\start-ai-coordinator.ps1
```

### 3. تشغيل مباشر:
```bash
docker-compose up -d ai-coordinator
```

## 🔌 API Endpoints الرئيسية

### **فحص الحالة:**
```bash
GET http://localhost:3333/api/health
```

### **التنسيق الذكي:**
```bash
POST http://localhost:3333/api/coordinate
Content-Type: application/json

{
  "prompt": "سؤالك هنا",
  "context": "السياق (اختياري)",
  "options": {
    "priority": "fast|normal|high",
    "complexity": "low|medium|high"
  }
}
```

### **التعاون بين النماذج:**
```bash
POST http://localhost:3333/api/collaborate
Content-Type: application/json

{
  "prompt": "سؤال معقد يحتاج عدة آراء",
  "context": "السياق"
}
```

## 🧠 منطق اتخاذ القرار

### **متى يستخدم Ollama:**
- المهام السريعة (`priority: "fast"`)
- النصوص القصيرة (< 100 حرف)
- المهام البرمجية (`context: "code"`)

### **متى يستخدم Gemini:**
- التحليل المعقد (`complexity: "high"`)
- البحث والدراسة (يحتوي على "research")
- المهام التي تحتاج دقة عالية

## 📊 أمثلة الاستخدام

### **PowerShell Examples:**

#### 1. فحص الحالة:
```powershell
Invoke-WebRequest -Uri "http://localhost:3333/api/health"
```

#### 2. سؤال سريع (سيستخدم Ollama):
```powershell
$body = @{
    prompt = "ما هو 2+2؟"
    options = @{priority = "fast"}
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3333/api/coordinate" -Method POST -Body $body -ContentType "application/json"
```

#### 3. تحليل معقد (سيستخدم Gemini):
```powershell
$body = @{
    prompt = "حلل اتجاهات الذكاء الاصطناعي في 2024"
    options = @{complexity = "high"}
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3333/api/coordinate" -Method POST -Body $body -ContentType "application/json"
```

#### 4. تعاون النماذج:
```powershell
$body = @{
    prompt = "ما أفضل لغة برمجة للمبتدئين؟"
} | ConvertTo-Json

Invoke-WebRequest -Uri "http://localhost:3333/api/collaborate" -Method POST -Body $body -ContentType "application/json"
```

## 🔧 التكامل مع AnythingLLM

### **إضافة Custom Function:**
في AnythingLLM، أضف هذه الدالة:

```javascript
async function aiCoordinator(prompt, options = {}) {
  const response = await fetch('http://localhost:3333/api/coordinate', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({ prompt, options })
  });
  
  const result = await response.json();
  return `[${result.decision.model}]: ${result.response}`;
}
```

### **الاستخدام في المحادثة:**
```
المستخدم: @aiCoordinator("اشرح الذكاء الاصطناعي", {"complexity": "high"})
النظام: [gemini]: الذكاء الاصطناعي هو...
```

## 🔄 التكامل مع n8n

### **إنشاء Workflow:**
1. أضف **Webhook Node** في n8n
2. اضبط URL: `http://ai-coordinator:3333/api/coordinate`
3. أضف **HTTP Request Node** للتواصل مع AI Coordinator

## 📁 ملفات التكوين

### **متغيرات البيئة (.env):**
```bash
# AI Coordinator
GEMINI_API_KEY=your-gemini-api-key-here
GOOGLE_API_KEY=your-google-api-key-here
NODE_ENV=production
DEBUG=false
```

### **Docker Compose:**
- تم دمج AI Coordinator في `docker-compose.yml`
- يعتمد على: Ollama, AnythingLLM, n8n
- شبكة موحدة: `ai-dev-network`

## 🛠️ إدارة النظام

### **عرض السجلات:**
```bash
docker-compose logs -f ai-coordinator
```

### **إعادة التشغيل:**
```bash
docker-compose restart ai-coordinator
```

### **فحص الحالة:**
```bash
docker-compose ps ai-coordinator
```

## 🔍 استكشاف الأخطاء

### **AI Coordinator لا يعمل:**
```bash
# فحص السجلات
docker-compose logs ai-coordinator

# فحص الحالة
curl http://localhost:3333/api/health
```

### **النماذج لا تستجيب:**
```bash
# فحص Ollama
curl http://localhost:11434/api/tags

# فحص Gemini API Key
echo $GEMINI_API_KEY
```

## 🎉 الخلاصة

**AI Coordinator** هو الآن المشروع الرئيسي الذي:
- ✅ ينسق بين جميع النماذج ذكياً
- ✅ يوفر API موحد للوصول
- ✅ يتخذ قرارات ذكية حول أي نموذج يستخدم
- ✅ متكامل مع AnythingLLM و n8n
- ✅ جاهز للاستخدام مع جميع النماذج المتاحة

**🚀 ابدأ الآن:** `.\start-ai-coordinator.ps1`
