# 🔌 **منافذ النظام الموحدة**

## 📊 **المنافذ الجديدة (نطاق 4000)**

| الخدمة | المنفذ الجديد | المنفذ القديم | الوصول |
|--------|-------------|-------------|--------|
| **🖥️ Ollama WebUI** | `4000` | `3000` | http://localhost:4000 |
| **💬 AnythingLLM** | `4001` | `3001` | http://localhost:4001 |
| **🔄 n8n** | `4002` | `5678` | http://localhost:4002 |
| **🧠 AI Coordinator** | `4003` | `3333` | http://localhost:4003 |
| **🤖 Ollama API** | `11434` | `11434` | http://localhost:11434 |

## 🎯 **مزايا التوحيد:**

### ✅ **حل التضارب:**
- **8080**: كان متضارب مع خدمات أخرى
- **3306**: MySQL محجوز
- **5432**: PostgreSQL محجوز

### 🔢 **نطاق موحد (4000-4003):**
- سهولة التذكر والإدارة
- تجنب التضارب مع الخدمات الأخرى
- تنظيم منطقي للمنافذ

### 🚀 **سهولة الوصول:**
```bash
# الخدمات الرئيسية
http://localhost:4000  # Ollama WebUI
http://localhost:4001  # AnythingLLM
http://localhost:4002  # n8n
http://localhost:4003  # AI Coordinator

# API
http://localhost:11434 # Ollama API
```

## 🔧 **للمطورين:**

### **Docker Compose:**
```yaml
ports:
  - "4000:8080"  # Ollama WebUI
  - "4001:3001"  # AnythingLLM
  - "4002:5678"  # n8n
  - "4003:3333"  # AI Coordinator
  - "11434:11434" # Ollama API
```

### **Environment Variables:**
```env
ANYTHINGLLM_PORT=4001
OLLAMA_WEBUI_PORT=4000
N8N_PORT=4002
AI_COORDINATOR_PORT=4003
OLLAMA_API_PORT=11434
```

## 📝 **ملاحظات:**

1. **Ollama API (11434)**: يبقى كما هو لأنه المنفذ الافتراضي
2. **نطاق 4000**: محجوز لمشروع AI Development Assistant
3. **Health Checks**: تم تحديثها لتعكس المنافذ الجديدة
4. **Webhooks**: تم تحديث URLs لتعكس المنافذ الجديدة

## 🔄 **التحديث:**

```bash
# إيقاف الخدمات القديمة
docker-compose down

# تشغيل الخدمات بالمنافذ الجديدة
docker-compose up -d

# التحقق من الحالة
docker-compose ps
```

---
**تم التحديث:** 2025-07-06  
**الإصدار:** 2.0 - منافذ موحدة
