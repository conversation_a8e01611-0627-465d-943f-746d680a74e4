# 🤖 AI Agents - AI Development Assistant

## 📋 **نظرة عامة:**

نظام وكلاء ذكي متخصص يستخدم نماذج Ollama المختلفة لتنفيذ مهام متنوعة بكفاءة عالية. كل وكيل مصمم لمهام محددة ويستخدم النموذج الأمثل لتلك المهام.

## 🤖 **الوكلاء المتاحين:**

### **🧠 Memory Agent** (`memory-agent.py`)
- **النموذج:** `gemma3n:e4b` (7.5GB)
- **التخصص:** إدارة الذاكرة والجلسات
- **المميزات:**
  - تحليل وفهرسة الجلسات
  - البحث الذكي في الذاكرة
  - تلخيص الجلسات
  - استخراج المعلومات المهمة
  - إنشاء تقارير شاملة

### **🔍 File Search Agent** (`file-search-agent.py`)
- **النموذج:** `llama3:8b` (4.6GB)
- **التخصص:** البحث والتحليل في الملفات
- **المميزات:**
  - البحث الذكي في الكود
  - تحليل هيكل المشروع
  - استخراج الوظائف والكلاسات
  - تحليل التبعيات
  - فهم أنواع الملفات المختلفة

### **💻 Terminal Agent** (`terminal-agent.py`)
- **النموذج:** `mistral:7b` (4.1GB)
- **التخصص:** إدارة الترمينال والنظام
- **المميزات:**
  - تحليل وتفسير الأوامر
  - اقتراح أوامر آمنة
  - مراقبة العمليات
  - كشف الأوامر الخطيرة
  - إدارة النظام الذكية

### **📊 Data Analysis Agent** (`data-analysis-agent.py`)
- **النموذج:** `phi3:mini` (2.2GB)
- **التخصص:** تحليل البيانات والإحصائيات
- **المميزات:**
  - تحليل ملفات CSV, JSON, Excel
  - إنشاء الرسوم البيانية
  - كشف القيم الشاذة
  - تحليل الاتجاهات
  - مقارنة مجموعات البيانات

### **🤖 Agent Coordinator** (`agent-coordinator.py`)
- **الوظيفة:** تنسيق العمل بين الوكلاء
- **المميزات:**
  - توجيه المهام للوكيل المناسب
  - إدارة قائمة المهام
  - دمج النتائج
  - واجهة موحدة

## 🚀 **التثبيت والإعداد:**

### **1. المتطلبات:**
```bash
pip install requests pandas numpy matplotlib seaborn psutil
```

### **2. التحقق من Ollama:**
```bash
# التحقق من النماذج المتاحة
ollama list

# تحميل النماذج المطلوبة (إذا لم تكن موجودة)
ollama pull gemma3n:e4b
ollama pull llama3:8b
ollama pull mistral:7b
ollama pull phi3:mini
```

### **3. تشغيل الوكلاء:**
```python
# تشغيل وكيل واحد
python memory-agent.py

# تشغيل المنسق (جميع الوكلاء)
python agent-coordinator.py
```

## 📖 **أمثلة الاستخدام:**

### **🧠 Memory Agent:**
```python
from memory_agent import MemoryAgent

agent = MemoryAgent()

# البحث في الذاكرة
results = agent.search_memory("docker ports")

# تحليل جلسة
analysis = agent.analyze_session("memory/sessions/session-2025-07-06-port-unification.md")

# إنشاء تقرير
report = agent.generate_session_report()
```

### **🔍 File Search Agent:**
```python
from file_search_agent import FileSearchAgent

agent = FileSearchAgent()

# البحث في الملفات
results = agent.search_in_files("docker", ".", max_results=5)

# تحليل ملف كود
analysis = agent.analyze_code_file("server.js")

# ملخص المشروع
summary = agent.generate_project_summary(".")
```

### **💻 Terminal Agent:**
```python
from terminal_agent import TerminalAgent

agent = TerminalAgent()

# تحليل أمر
analysis = agent.analyze_command("docker ps")

# اقتراح أمر
suggestion = agent.suggest_command("عرض استخدام القرص")

# تنفيذ أمر آمن
result = agent.execute_command("ls -la")
```

### **📊 Data Analysis Agent:**
```python
from data_analysis_agent import DataAnalysisAgent

agent = DataAnalysisAgent()

# تحليل ملف بيانات
analysis = agent.analyze_file("data.csv")

# مقارنة مجموعتي بيانات
comparison = agent.compare_datasets("data1.csv", "data2.csv")

# إنشاء رسم بياني
chart = agent.create_visualization(df, "bar", "category", "value")
```

### **🤖 Agent Coordinator:**
```python
from agent_coordinator import AgentCoordinator

coordinator = AgentCoordinator()

# إضافة مهام
coordinator.add_task("البحث عن ملفات Python", {"action": "search", "query": "python"})
coordinator.add_task("تحليل استخدام النظام", {"action": "system_info"})

# معالجة المهام
results = coordinator.process_queue()
```

## 🔧 **التكوين:**

### **متغيرات البيئة:**
```bash
# رابط Ollama (افتراضي: http://localhost:11434)
export OLLAMA_URL="http://localhost:11434"

# مجلد الذاكرة (افتراضي: memory)
export MEMORY_PATH="memory"

# مجلد الرسوم البيانية (افتراضي: charts)
export CHARTS_PATH="charts"
```

### **تخصيص النماذج:**
```python
# في كل وكيل، يمكن تغيير النموذج
agent = MemoryAgent()
agent.model = "llama3:8b"  # تغيير النموذج
```

## 📊 **الأداء والموارد:**

| الوكيل | النموذج | الحجم | السرعة | الاستخدام الأمثل |
|--------|---------|-------|--------|------------------|
| Memory | gemma3n:e4b | 7.5GB | متوسط | تحليل عميق للذاكرة |
| File Search | llama3:8b | 4.6GB | سريع | فهم الكود والملفات |
| Terminal | mistral:7b | 4.1GB | سريع جداً | مهام تفاعلية |
| Data Analysis | phi3:mini | 2.2GB | سريع جداً | حسابات سريعة |

## 🔒 **الأمان:**

### **Terminal Agent:**
- كشف الأوامر الخطيرة تلقائياً
- طلب تأكيد للأوامر المدمرة
- مهلة زمنية للأوامر (30 ثانية)
- تسجيل جميع الأوامر المنفذة

### **File Search Agent:**
- استبعاد الملفات الحساسة
- حماية من الوصول خارج المجلد المحدد
- فلترة أنواع الملفات المدعومة

## 🐛 **استكشاف الأخطاء:**

### **مشاكل شائعة:**

1. **Ollama غير متاح:**
   ```bash
   # التحقق من حالة Ollama
   curl http://localhost:11434/api/tags
   ```

2. **نموذج غير موجود:**
   ```bash
   # تحميل النموذج
   ollama pull model_name
   ```

3. **خطأ في الذاكرة:**
   ```bash
   # تحرير ذاكرة Ollama
   ollama stop model_name
   ```

## 📈 **التطوير المستقبلي:**

### **مميزات مخططة:**
- [ ] وكيل للشبكات والأمان
- [ ] وكيل لإدارة قواعد البيانات
- [ ] واجهة ويب للتحكم
- [ ] API REST للوكلاء
- [ ] تكامل مع خدمات السحابة

### **تحسينات:**
- [ ] تحسين استخدام الذاكرة
- [ ] دعم المعالجة المتوازية
- [ ] نظام تخزين مؤقت للنتائج
- [ ] مراقبة الأداء المتقدمة

## 📞 **الدعم:**

للمساعدة أو الإبلاغ عن مشاكل:
1. راجع ملفات الجلسات في `memory/sessions/`
2. تحقق من سجلات Ollama
3. اختبر الوكلاء منفردين قبل استخدام المنسق

---
**تم الإنشاء:** 2025-07-06  
**الإصدار:** 1.0  
**المطور:** AI Development Assistant
