#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
📊 Data Analysis Agent - AI Development Assistant
=================================================
وكيل متخصص في تحليل البيانات والإحصائيات باستخدام Ollama

المميزات:
- تحليل البيانات الإحصائي
- إنشاء التقارير والرسوم البيانية
- تحليل الاتجاهات والأنماط
- معالجة ملفات CSV, JSON, Excel
- تحليل الأداء والمقاييس
- اقتراح التحسينات

النموذج المستخدم: phi3:mini (سريع للحسابات)
"""

import json
import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import statistics
import datetime
import io
import base64

class DataAnalysisAgent:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        """
        تهيئة وكيل تحليل البيانات
        
        Args:
            ollama_url: رابط خادم Ollama
        """
        self.ollama_url = ollama_url
        self.model = "phi3:mini"  # نموذج سريع للحسابات
        
        # إعداد matplotlib للعربية
        plt.rcParams['font.family'] = ['Arial Unicode MS', 'Tahoma', 'DejaVu Sans']
        
        # أنواع الملفات المدعومة
        self.supported_formats = ['.csv', '.json', '.xlsx', '.xls', '.tsv']
        
        print(f"📊 Data Analysis Agent initialized with model: {self.model}")
    
    def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """
        استدعاء نموذج Ollama
        
        Args:
            prompt: النص المطلوب معالجته
            system_prompt: تعليمات النظام
            
        Returns:
            الرد من النموذج
        """
        try:
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            if system_prompt:
                data["system"] = system_prompt
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=data,
                timeout=45
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                return f"خطأ في الاتصال: {response.status_code}"
                
        except Exception as e:
            return f"خطأ: {str(e)}"
    
    def load_data(self, file_path: str) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        تحميل البيانات من ملف
        
        Args:
            file_path: مسار الملف
            
        Returns:
            DataFrame والمعلومات الأساسية
        """
        try:
            file_ext = Path(file_path).suffix.lower()
            
            if file_ext == '.csv':
                df = pd.read_csv(file_path)
            elif file_ext == '.json':
                df = pd.read_json(file_path)
            elif file_ext in ['.xlsx', '.xls']:
                df = pd.read_excel(file_path)
            elif file_ext == '.tsv':
                df = pd.read_csv(file_path, sep='\t')
            else:
                raise ValueError(f"نوع الملف غير مدعوم: {file_ext}")
            
            info = {
                "file_path": file_path,
                "shape": df.shape,
                "columns": list(df.columns),
                "dtypes": df.dtypes.to_dict(),
                "memory_usage": df.memory_usage(deep=True).sum(),
                "null_counts": df.isnull().sum().to_dict()
            }
            
            return df, info
            
        except Exception as e:
            raise Exception(f"خطأ في تحميل البيانات: {str(e)}")
    
    def basic_statistics(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        حساب الإحصائيات الأساسية
        
        Args:
            df: DataFrame للتحليل
            
        Returns:
            الإحصائيات الأساسية
        """
        try:
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            categorical_cols = df.select_dtypes(include=['object']).columns
            
            stats = {
                "numeric_summary": {},
                "categorical_summary": {},
                "correlations": {},
                "missing_data": df.isnull().sum().to_dict()
            }
            
            # إحصائيات الأعمدة الرقمية
            if len(numeric_cols) > 0:
                stats["numeric_summary"] = df[numeric_cols].describe().to_dict()
                
                # الارتباطات
                if len(numeric_cols) > 1:
                    stats["correlations"] = df[numeric_cols].corr().to_dict()
            
            # إحصائيات الأعمدة النصية
            if len(categorical_cols) > 0:
                for col in categorical_cols:
                    stats["categorical_summary"][col] = {
                        "unique_count": df[col].nunique(),
                        "top_values": df[col].value_counts().head().to_dict()
                    }
            
            return stats
            
        except Exception as e:
            return {"error": str(e)}
    
    def analyze_trends(self, df: pd.DataFrame, date_column: str = None, value_column: str = None) -> Dict[str, Any]:
        """
        تحليل الاتجاهات الزمنية
        
        Args:
            df: DataFrame للتحليل
            date_column: عمود التاريخ
            value_column: عمود القيم
            
        Returns:
            تحليل الاتجاهات
        """
        try:
            if date_column and date_column in df.columns:
                # تحويل عمود التاريخ
                df[date_column] = pd.to_datetime(df[date_column])
                df_sorted = df.sort_values(date_column)
                
                analysis = {
                    "date_range": {
                        "start": df_sorted[date_column].min().isoformat(),
                        "end": df_sorted[date_column].max().isoformat()
                    },
                    "trends": {}
                }
                
                if value_column and value_column in df.columns:
                    values = df_sorted[value_column].dropna()
                    
                    # حساب الاتجاه
                    if len(values) > 1:
                        x = np.arange(len(values))
                        slope = np.polyfit(x, values, 1)[0]
                        
                        analysis["trends"][value_column] = {
                            "slope": slope,
                            "direction": "صاعد" if slope > 0 else "هابط" if slope < 0 else "ثابت",
                            "min": values.min(),
                            "max": values.max(),
                            "mean": values.mean(),
                            "std": values.std()
                        }
                
                return analysis
            else:
                return {"error": "عمود التاريخ غير محدد أو غير موجود"}
                
        except Exception as e:
            return {"error": str(e)}
    
    def detect_outliers(self, df: pd.DataFrame, column: str) -> Dict[str, Any]:
        """
        كشف القيم الشاذة
        
        Args:
            df: DataFrame للتحليل
            column: اسم العمود
            
        Returns:
            تحليل القيم الشاذة
        """
        try:
            if column not in df.columns:
                return {"error": f"العمود {column} غير موجود"}
            
            data = df[column].dropna()
            
            if not pd.api.types.is_numeric_dtype(data):
                return {"error": "العمود يجب أن يكون رقمي"}
            
            # طريقة IQR
            Q1 = data.quantile(0.25)
            Q3 = data.quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            
            outliers = data[(data < lower_bound) | (data > upper_bound)]
            
            # طريقة Z-score
            z_scores = np.abs(statistics.zscore(data))
            z_outliers = data[z_scores > 3]
            
            return {
                "column": column,
                "total_values": len(data),
                "iqr_method": {
                    "outliers_count": len(outliers),
                    "outliers_percentage": len(outliers) / len(data) * 100,
                    "lower_bound": lower_bound,
                    "upper_bound": upper_bound,
                    "outliers": outliers.tolist()
                },
                "zscore_method": {
                    "outliers_count": len(z_outliers),
                    "outliers_percentage": len(z_outliers) / len(data) * 100,
                    "outliers": z_outliers.tolist()
                }
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def generate_insights(self, df: pd.DataFrame, stats: Dict[str, Any]) -> str:
        """
        إنشاء رؤى ذكية من البيانات
        
        Args:
            df: DataFrame المحلل
            stats: الإحصائيات المحسوبة
            
        Returns:
            رؤى وتوصيات
        """
        system_prompt = """أنت محلل بيانات خبير. بناءً على الإحصائيات المقدمة، قدم:
1. ملخص عن البيانات
2. الأنماط والاتجاهات المهمة
3. القيم الشاذة والمشاكل المحتملة
4. توصيات للتحسين أو التحليل الإضافي
5. رؤى مفيدة للأعمال

اجعل التحليل واضح ومفيد لصانع القرار."""

        data_summary = f"""
شكل البيانات: {df.shape}
الأعمدة: {list(df.columns)}
الإحصائيات: {json.dumps(stats, indent=2, ensure_ascii=False, default=str)}
"""

        return self._call_ollama(data_summary, system_prompt)
    
    def create_visualization(self, df: pd.DataFrame, chart_type: str, x_column: str, y_column: str = None) -> str:
        """
        إنشاء رسم بياني
        
        Args:
            df: DataFrame للرسم
            chart_type: نوع الرسم (bar, line, scatter, hist)
            x_column: عمود المحور السيني
            y_column: عمود المحور الصادي
            
        Returns:
            مسار الملف المحفوظ
        """
        try:
            plt.figure(figsize=(10, 6))
            
            if chart_type == "bar":
                if y_column:
                    plt.bar(df[x_column], df[y_column])
                else:
                    df[x_column].value_counts().plot(kind='bar')
                plt.title(f"رسم بياني عمودي: {x_column}")
                
            elif chart_type == "line":
                if y_column:
                    plt.plot(df[x_column], df[y_column])
                else:
                    df[x_column].plot()
                plt.title(f"رسم بياني خطي: {x_column}")
                
            elif chart_type == "scatter":
                if y_column:
                    plt.scatter(df[x_column], df[y_column])
                    plt.title(f"رسم بياني نقطي: {x_column} vs {y_column}")
                else:
                    return "رسم النقاط يحتاج عمودين"
                    
            elif chart_type == "hist":
                plt.hist(df[x_column], bins=20)
                plt.title(f"توزيع تكراري: {x_column}")
                
            else:
                return f"نوع الرسم غير مدعوم: {chart_type}"
            
            plt.xlabel(x_column)
            if y_column:
                plt.ylabel(y_column)
            
            plt.tight_layout()
            
            # حفظ الرسم
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"chart_{chart_type}_{timestamp}.png"
            filepath = Path("charts") / filename
            filepath.parent.mkdir(exist_ok=True)
            
            plt.savefig(filepath, dpi=300, bbox_inches='tight')
            plt.close()
            
            return str(filepath)
            
        except Exception as e:
            return f"خطأ في إنشاء الرسم: {str(e)}"
    
    def analyze_file(self, file_path: str) -> Dict[str, Any]:
        """
        تحليل شامل لملف بيانات
        
        Args:
            file_path: مسار الملف
            
        Returns:
            تحليل شامل
        """
        try:
            # تحميل البيانات
            df, file_info = self.load_data(file_path)
            
            # الإحصائيات الأساسية
            stats = self.basic_statistics(df)
            
            # إنشاء الرؤى
            insights = self.generate_insights(df, stats)
            
            # تحليل القيم الشاذة للأعمدة الرقمية
            numeric_cols = df.select_dtypes(include=[np.number]).columns
            outliers_analysis = {}
            
            for col in numeric_cols[:3]:  # أول 3 أعمدة رقمية
                outliers_analysis[col] = self.detect_outliers(df, col)
            
            return {
                "file_info": file_info,
                "basic_stats": stats,
                "insights": insights,
                "outliers": outliers_analysis,
                "timestamp": datetime.datetime.now().isoformat()
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def compare_datasets(self, file1: str, file2: str) -> Dict[str, Any]:
        """
        مقارنة مجموعتي بيانات
        
        Args:
            file1: مسار الملف الأول
            file2: مسار الملف الثاني
            
        Returns:
            مقارنة شاملة
        """
        try:
            df1, info1 = self.load_data(file1)
            df2, info2 = self.load_data(file2)
            
            comparison = {
                "file1": info1,
                "file2": info2,
                "shape_comparison": {
                    "file1_shape": df1.shape,
                    "file2_shape": df2.shape,
                    "rows_diff": df2.shape[0] - df1.shape[0],
                    "cols_diff": df2.shape[1] - df1.shape[1]
                },
                "columns_comparison": {
                    "common_columns": list(set(df1.columns) & set(df2.columns)),
                    "file1_only": list(set(df1.columns) - set(df2.columns)),
                    "file2_only": list(set(df2.columns) - set(df1.columns))
                }
            }
            
            # مقارنة الإحصائيات للأعمدة المشتركة
            common_numeric = []
            for col in comparison["columns_comparison"]["common_columns"]:
                if pd.api.types.is_numeric_dtype(df1[col]) and pd.api.types.is_numeric_dtype(df2[col]):
                    common_numeric.append(col)
            
            if common_numeric:
                comparison["numeric_comparison"] = {}
                for col in common_numeric[:3]:  # أول 3 أعمدة
                    comparison["numeric_comparison"][col] = {
                        "file1_mean": df1[col].mean(),
                        "file2_mean": df2[col].mean(),
                        "mean_diff": df2[col].mean() - df1[col].mean(),
                        "file1_std": df1[col].std(),
                        "file2_std": df2[col].std()
                    }
            
            return comparison
            
        except Exception as e:
            return {"error": str(e)}

def main():
    """دالة الاختبار الرئيسية"""
    print("📊 Data Analysis Agent - AI Development Assistant")
    print("=" * 50)
    
    # تهيئة الوكيل
    agent = DataAnalysisAgent()
    
    # إنشاء بيانات تجريبية
    print("\n📝 إنشاء بيانات تجريبية:")
    sample_data = {
        "التاريخ": pd.date_range("2024-01-01", periods=100, freq="D"),
        "المبيعات": np.random.normal(1000, 200, 100),
        "العملاء": np.random.poisson(50, 100),
        "المنطقة": np.random.choice(["الشمال", "الجنوب", "الشرق", "الغرب"], 100)
    }
    
    df = pd.DataFrame(sample_data)
    df.to_csv("sample_data.csv", index=False)
    print("تم إنشاء sample_data.csv")
    
    # تحليل البيانات
    print("\n📊 تحليل البيانات:")
    analysis = agent.analyze_file("sample_data.csv")
    
    if "error" not in analysis:
        print(f"شكل البيانات: {analysis['file_info']['shape']}")
        print(f"الأعمدة: {analysis['file_info']['columns']}")
        print(f"الرؤى: {analysis['insights'][:200]}...")
    else:
        print(f"خطأ: {analysis['error']}")

if __name__ == "__main__":
    main()
