#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧠 Memory Agent - AI Development Assistant
==========================================
وكيل متخصص في إدارة الذاكرة والجلسات باستخدام Ollama

المميزات:
- إدارة الجلسات والذاكرة
- تحليل وفهرسة المحتوى
- البحث الذكي في الذاكرة
- تلخيص الجلسات
- استخراج المعلومات المهمة

النموذج المستخدم: gemma3n:e4b (للذاكرة طويلة المدى)
"""

import json
import requests
import datetime
import os
import re
from pathlib import Path
from typing import Dict, List, Optional, Any

class MemoryAgent:
    def __init__(self, ollama_url: str = "http://localhost:11434"):
        """
        تهيئة وكيل الذاكرة
        
        Args:
            ollama_url: رابط خادم Ollama
        """
        self.ollama_url = ollama_url
        self.model = "gemma3n:e4b"  # نموذج قوي للذاكرة
        self.memory_path = Path("memory")
        self.sessions_path = self.memory_path / "sessions"
        
        # إنشاء المجلدات إذا لم تكن موجودة
        self.memory_path.mkdir(exist_ok=True)
        self.sessions_path.mkdir(exist_ok=True)
        
        print(f"🧠 Memory Agent initialized with model: {self.model}")
    
    def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """
        استدعاء نموذج Ollama
        
        Args:
            prompt: النص المطلوب معالجته
            system_prompt: تعليمات النظام
            
        Returns:
            الرد من النموذج
        """
        try:
            data = {
                "model": self.model,
                "prompt": prompt,
                "stream": False
            }
            
            if system_prompt:
                data["system"] = system_prompt
            
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json=data,
                timeout=60
            )
            
            if response.status_code == 200:
                return response.json().get("response", "")
            else:
                return f"خطأ في الاتصال: {response.status_code}"
                
        except Exception as e:
            return f"خطأ: {str(e)}"
    
    def analyze_session(self, session_file: str) -> Dict[str, Any]:
        """
        تحليل جلسة عمل
        
        Args:
            session_file: مسار ملف الجلسة
            
        Returns:
            تحليل شامل للجلسة
        """
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            system_prompt = """أنت محلل ذكي للجلسات. قم بتحليل محتوى الجلسة وإستخراج:
1. الموضوع الرئيسي
2. الأهداف المحققة
3. المشاكل المواجهة والحلول
4. الدروس المستفادة
5. الكلمات المفتاحية
6. التوصيات للمستقبل

اجعل التحليل مفصل ومفيد للمراجعة المستقبلية."""

            prompt = f"حلل هذه الجلسة:\n\n{content}"
            
            analysis = self._call_ollama(prompt, system_prompt)
            
            return {
                "file": session_file,
                "analysis": analysis,
                "timestamp": datetime.datetime.now().isoformat(),
                "word_count": len(content.split()),
                "char_count": len(content)
            }
            
        except Exception as e:
            return {"error": str(e)}
    
    def search_memory(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """
        البحث في الذاكرة
        
        Args:
            query: استعلام البحث
            limit: عدد النتائج المطلوبة
            
        Returns:
            نتائج البحث مع التقييم
        """
        results = []
        
        # البحث في ملفات الجلسات
        for session_file in self.sessions_path.glob("*.md"):
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # تقييم الصلة باستخدام Ollama
                system_prompt = f"""قيم مدى صلة هذا المحتوى بالاستعلام: "{query}"
أعط درجة من 0 إلى 10 حيث:
- 0: غير مرتبط إطلاقاً
- 5: مرتبط جزئياً
- 10: مرتبط تماماً

أجب بالرقم فقط."""

                relevance_score = self._call_ollama(
                    f"المحتوى:\n{content[:1000]}...",
                    system_prompt
                )
                
                try:
                    score = float(re.search(r'\d+', relevance_score).group())
                except:
                    score = 0
                
                if score >= 3:  # عتبة الصلة
                    results.append({
                        "file": str(session_file),
                        "score": score,
                        "preview": content[:200] + "...",
                        "size": len(content)
                    })
                    
            except Exception as e:
                continue
        
        # ترتيب النتائج حسب الدرجة
        results.sort(key=lambda x: x["score"], reverse=True)
        return results[:limit]
    
    def summarize_session(self, session_file: str) -> str:
        """
        تلخيص جلسة عمل
        
        Args:
            session_file: مسار ملف الجلسة
            
        Returns:
            ملخص الجلسة
        """
        try:
            with open(session_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            system_prompt = """أنت خبير في تلخيص الجلسات التقنية. 
اكتب ملخص مختصر ومفيد يتضمن:
- الهدف الرئيسي
- ما تم إنجازه
- المشاكل والحلول
- النتائج المهمة

اجعل الملخص واضح ومفيد للمراجعة السريعة."""

            prompt = f"لخص هذه الجلسة:\n\n{content}"
            
            return self._call_ollama(prompt, system_prompt)
            
        except Exception as e:
            return f"خطأ في التلخيص: {str(e)}"
    
    def extract_keywords(self, text: str) -> List[str]:
        """
        استخراج الكلمات المفتاحية
        
        Args:
            text: النص المطلوب تحليله
            
        Returns:
            قائمة الكلمات المفتاحية
        """
        system_prompt = """استخرج أهم الكلمات المفتاحية من النص.
ركز على:
- المصطلحات التقنية
- أسماء التقنيات والأدوات
- المفاهيم المهمة
- أسماء الملفات والمجلدات

أعط قائمة مفصولة بفواصل."""

        keywords_text = self._call_ollama(text[:1000], system_prompt)
        
        # تنظيف وتقسيم الكلمات المفتاحية
        keywords = [kw.strip() for kw in keywords_text.split(',')]
        return [kw for kw in keywords if kw and len(kw) > 2]
    
    def generate_session_report(self) -> Dict[str, Any]:
        """
        إنشاء تقرير شامل عن الجلسات
        
        Returns:
            تقرير تفصيلي
        """
        report = {
            "timestamp": datetime.datetime.now().isoformat(),
            "total_sessions": 0,
            "total_words": 0,
            "sessions": [],
            "top_keywords": [],
            "summary": ""
        }
        
        # تحليل جميع الجلسات
        for session_file in self.sessions_path.glob("*.md"):
            try:
                with open(session_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                word_count = len(content.split())
                keywords = self.extract_keywords(content)
                
                report["sessions"].append({
                    "file": str(session_file.name),
                    "word_count": word_count,
                    "keywords": keywords[:5]  # أهم 5 كلمات
                })
                
                report["total_words"] += word_count
                report["total_sessions"] += 1
                
            except Exception as e:
                continue
        
        # إنشاء ملخص عام
        if report["total_sessions"] > 0:
            system_prompt = """أنت محلل بيانات للجلسات التقنية.
اكتب ملخص عام عن التقدم والإنجازات بناءً على البيانات المقدمة."""

            sessions_summary = "\n".join([
                f"- {s['file']}: {s['word_count']} كلمة"
                for s in report["sessions"]
            ])
            
            report["summary"] = self._call_ollama(
                f"إجمالي الجلسات: {report['total_sessions']}\n"
                f"إجمالي الكلمات: {report['total_words']}\n\n"
                f"الجلسات:\n{sessions_summary}",
                system_prompt
            )
        
        return report

def main():
    """دالة الاختبار الرئيسية"""
    print("🧠 Memory Agent - AI Development Assistant")
    print("=" * 50)
    
    # تهيئة الوكيل
    agent = MemoryAgent()
    
    # اختبار البحث
    print("\n🔍 اختبار البحث في الذاكرة:")
    results = agent.search_memory("docker ports")
    for result in results:
        print(f"- {result['file']}: درجة {result['score']}")
    
    # إنشاء تقرير
    print("\n📊 إنشاء تقرير الجلسات:")
    report = agent.generate_session_report()
    print(f"إجمالي الجلسات: {report['total_sessions']}")
    print(f"إجمالي الكلمات: {report['total_words']}")
    
    if report['summary']:
        print(f"\nالملخص العام:\n{report['summary']}")

if __name__ == "__main__":
    main()
