{"name": "anything-llm-frontend", "private": false, "license": "MIT", "type": "module", "scripts": {"start": "vite --open", "dev": "cross-env NODE_ENV=development vite --debug --host=0.0.0.0", "build": "vite build && node scripts/postbuild.js", "lint": "yarn prettier --ignore-path ../.prettierignore --write ./src", "preview": "vite preview"}, "dependencies": {"@microsoft/fetch-event-source": "^2.0.1", "@mintplex-labs/piper-tts-web": "^1.0.4", "@phosphor-icons/react": "^2.1.7", "@tremor/react": "^3.15.1", "dompurify": "^3.0.8", "file-saver": "^2.0.5", "he": "^1.2.0", "highlight.js": "^11.9.0", "i18next": "^23.11.3", "i18next-browser-languagedetector": "^7.2.1", "js-levenshtein": "^1.1.6", "katex": "^0.6.0", "lodash.debounce": "^4.0.8", "markdown-it": "^13.0.1", "moment": "^2.30.1", "onnxruntime-web": "^1.18.0", "pluralize": "^8.0.0", "react": "^18.2.0", "react-beautiful-dnd": "13.1.1", "react-confetti-explosion": "^2.1.2", "react-device-detect": "^2.2.2", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-highlight-words": "^0.21.0", "react-i18next": "^14.1.1", "react-loading-skeleton": "^3.1.0", "react-router-dom": "^6.3.0", "react-speech-recognition": "^3.10.0", "react-tag-input-component": "^2.0.2", "react-toastify": "^9.1.3", "react-tooltip": "^5.25.2", "recharts": "^2.12.5", "recharts-to-png": "^2.3.1", "text-case": "^1.0.9", "truncate": "^3.0.0", "uuid": "^9.0.0"}, "devDependencies": {"@esbuild-plugins/node-globals-polyfill": "^0.1.1", "@types/react": "^18.2.23", "@types/react-dom": "^18.2.8", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "^4.0.0-beta.0", "autoprefixer": "^10.4.14", "buffer": "^6.0.3", "cross-env": "^7.0.3", "eslint": "^8.50.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-ft-flow": "^3.0.0", "eslint-plugin-prettier": "^5.0.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "flow-bin": "^0.217.0", "flow-remove-types": "^2.217.1", "globals": "^13.21.0", "hermes-eslint": "^0.15.0", "postcss": "^8.4.23", "prettier": "^3.0.3", "rollup-plugin-visualizer": "^5.9.0", "tailwindcss": "^3.3.1", "vite": "^4.3.0"}}