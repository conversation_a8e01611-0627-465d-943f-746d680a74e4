@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  -webkit-font-smoothing: antialiased;
}

:root {
  /* Default theme */
  --theme-loader: #ffffff;
  --theme-bg-primary: #0e0f0f;
  --theme-bg-secondary: #1b1b1e;
  --theme-bg-sidebar: #0e0f0f;
  --theme-bg-container: #0e0f0f;
  --theme-bg-chat: #1b1b1e;
  --theme-bg-chat-input: #27282a;
  --theme-text-primary: #ffffff;
  --theme-text-secondary: rgba(255, 255, 255, 0.6);
  --theme-placeholder: #57585a;
  --theme-sidebar-item-default: rgba(255, 255, 255, 0.1);
  --theme-sidebar-item-selected: rgba(255, 255, 255, 0.3);
  --theme-sidebar-item-hover: #3f3f42;
  --theme-sidebar-subitem-default: rgba(255, 255, 255, 0.05);
  --theme-sidebar-subitem-selected: rgba(255, 255, 255, 0.05);
  --theme-sidebar-thread-selected: rgba(255, 255, 255, 0.05);
  --theme-popup-menu-bg: #000000;

  --theme-sidebar-subitem-hover: rgba(255, 255, 255, 0.05);
  --theme-sidebar-border: rgba(255, 255, 255, 0.1);
  --theme-sidebar-item-workspace-active: #ffffff;
  --theme-sidebar-item-workspace-inactive: #ffffff;

  --theme-sidebar-footer-icon: rgba(255, 255, 255, 0.1);
  --theme-sidebar-footer-icon-fill: #ffffff;
  --theme-sidebar-footer-icon-hover: rgba(255, 255, 255, 0.2);

  --theme-chat-input-border: #525355;
  --theme-action-menu-bg: #27282a;
  --theme-action-menu-item-hover: rgba(255, 255, 255, 0.1);
  --theme-settings-input-bg: #0e0f0f;
  --theme-settings-input-placeholder: rgba(255, 255, 255, 0.5);
  --theme-settings-input-active: rgb(255 255 255 / 0.2);
  --theme-settings-input-text: #ffffff;
  --theme-modal-border: #3f3f42;

  --theme-button-primary: #46c8ff;
  --theme-button-primary-hover: #434343;

  --theme-button-cta: #7cd4fd;

  --theme-file-row-even: #0e0f0f;
  --theme-file-row-odd: #1b1b1e;
  --theme-file-row-selected-even: rgba(14, 165, 233, 0.2);
  --theme-file-row-selected-odd: rgba(14, 165, 233, 0.1);
  --theme-file-picker-hover: rgb(14 165 233 / 0.2);

  --theme-home-text: #ffffff;
  --theme-home-text-secondary: #9f9fa0;
  --theme-home-bg-card: #1a1b1b;
  --theme-home-bg-button: #252626;
  --theme-home-border: rgba(255, 255, 255, 0.2);
  --theme-home-button-primary: #36bffa;
  --theme-home-button-primary-hover: rgba(54, 191, 250, 0.9);
  --theme-home-button-secondary: #27282a;
  --theme-home-button-secondary-hover: rgba(54, 191, 250, 0.1);
  --theme-home-button-secondary-text: #ffffff;
  --theme-home-button-secondary-hover-text: #36bffa;
  --theme-home-update-card-bg: #1c1c1c;
  --theme-home-update-card-hover: #252525;
  --theme-home-update-source: #53b1fd;

  --theme-checklist-item-bg: #203c48;
  --theme-checklist-item-bg-hover: #255d75;
  --theme-checklist-item-text: #b9e6fe;
  --theme-checklist-item-completed-bg: #36463d;
  --theme-checklist-item-completed-text: #a6f4c5;
  --theme-checklist-checkbox-fill: #a6f4c5;
  --theme-checklist-checkbox-text: #36463d;
  --theme-checklist-item-hover: #36bffa;
  --theme-checklist-checkbox-border: #ffffff;
  --theme-checklist-button-border: #36bffa;
  --theme-checklist-button-text: #36bffa;
  --theme-checklist-button-hover-bg: rgba(54, 191, 250, 0.2);
  --theme-checklist-button-hover-border: rgba(54, 191, 250, 0.8);

  --theme-home-button-secondary-border: #acc1e6;
  --theme-home-button-secondary-border-hover: #293056;

  --theme-attachment-bg: #18191a;
  --theme-attachment-error-bg: rgba(180, 35, 24, 0.4);
  --theme-attachment-success-bg: #18191a;
  --theme-attachment-text: #ffffff;
  --theme-attachment-text-secondary: rgba(255, 255, 255, 0.8);
  --theme-attachment-icon: #ffffff;
  --theme-attachment-icon-spinner: #ffffff;
  --theme-attachment-icon-spinner-bg: #27282a;

  --theme-button-text: #a8a9ab;
  --theme-button-code-hover-text: #7cd4fd;
  --theme-button-code-hover-bg: #22343f;
  --theme-button-disable-hover-text: #fec84b;
  --theme-button-disable-hover-bg: #3a3128;
  --theme-button-delete-hover-text: #f97066;
  --theme-button-delete-hover-bg: #37282b;
}

[data-theme="light"] {
  --theme-loader: #000000;
  --theme-bg-primary: #ffffff;
  --theme-bg-secondary: #ffffff;
  --theme-bg-sidebar: #edf2fa;
  --theme-bg-container: #f9fbfd;
  --theme-popup-menu-bg: #c2e7fe;

  --theme-bg-chat: #ffffff;
  --theme-bg-chat-input: #eaeaea;
  --theme-text-primary: #0e0f0f;
  --theme-text-secondary: #7a7d7e;
  --theme-placeholder: #9ca3af;
  --theme-sidebar-item-default: #ffffff;
  --theme-sidebar-item-selected: #ffffff;
  --theme-sidebar-item-hover: #c8efff;

  --theme-sidebar-item-text-inactive: #7a7d7e;
  --theme-sidebar-item-text-active: #184558;

  --theme-sidebar-item-workspace-active: #000000;
  --theme-sidebar-item-workspace-inactive: #7a7d7e;

  --theme-sidebar-subitem-default: transparent;
  --theme-sidebar-subitem-selected: #e2e7ee;
  --theme-sidebar-thread-selected: #ffffff;
  --theme-sidebar-subitem-hover: #e2e7ee;
  --theme-sidebar-border: #d3d4d4;

  --theme-sidebar-footer-icon: #ffffff;
  --theme-sidebar-footer-icon-fill: #6e6f6f;
  --theme-sidebar-footer-icon-hover: #d8d6d6;

  --theme-chat-input-border: #cccccc;
  --theme-action-menu-bg: #eaeaea;
  --theme-action-menu-item-hover: rgba(0, 0, 0, 0.1);
  --theme-settings-input-bg: #edf2fa;
  --theme-settings-input-placeholder: rgba(0, 0, 0, 0.5);
  --theme-settings-input-active: rgb(0 0 0 / 0.2);
  --theme-settings-input-text: #0e0f0f;
  --theme-modal-border: #d3d3d3;

  --theme-button-primary: #0ba5ec;
  --theme-button-primary-hover: #dedede;

  --theme-button-cta: #7cd4fd;

  --theme-file-row-even: #f5f5f5;
  --theme-file-row-odd: #e9e9e9;
  --theme-file-row-selected-even: #0ba5ec;
  --theme-file-row-selected-odd: #0ba5ec;
  --theme-file-picker-hover: #e2e7ee;

  --theme-home-text: #0e0f0f;
  --theme-home-text-secondary: #6f6f71;
  --theme-home-bg-card: #edf2fa;
  --theme-home-bg-button: #f3f4f6;
  --theme-home-border: rgba(0, 0, 0, 0.1);
  --theme-home-button-primary: #36bffa;
  --theme-home-button-primary-hover: rgba(54, 191, 250, 0.9);
  --theme-home-button-secondary: #dbe8fe;
  --theme-home-button-secondary-hover: #b0c8f1;
  --theme-home-button-secondary-text: #293056;
  --theme-home-button-secondary-hover-text: #293056;
  --theme-home-update-card-bg: #edf2fa;
  --theme-home-update-card-hover: #f3f4f6;
  --theme-home-update-source: #0284c7;

  --theme-checklist-item-bg: #c7e2ee;
  --theme-checklist-item-bg-hover: #a3d9f1;
  --theme-checklist-item-text: #0d3851;
  --theme-checklist-item-completed-bg: #d8f3ea;
  --theme-checklist-item-completed-text: #039855;
  --theme-checklist-checkbox-fill: #6ce9a6;
  --theme-checklist-checkbox-text: #ffffff;
  --theme-checklist-item-hover: #0ba5ec;
  --theme-checklist-checkbox-border: #6b7280;
  --theme-checklist-button-border: #0ba5ec;
  --theme-checklist-button-text: #0ba5ec;
  --theme-checklist-button-hover-bg: rgba(11, 165, 236, 0.1);
  --theme-checklist-button-hover-border: rgba(11, 165, 236, 0.8);

  --theme-home-button-secondary-border-hover: #293056;

  --theme-attachment-bg: #edf2fa;
  --theme-attachment-error-bg: rgba(180, 35, 24, 0.3);
  --theme-attachment-success-bg: #eaeaea;
  --theme-attachment-text: #0e0f0f;
  --theme-attachment-text-secondary: rgba(0, 0, 0, 0.8);
  --theme-attachment-icon: #ffffff;
  --theme-attachment-icon-spinner: #7cd4fd;
  --theme-attachment-icon-spinner-bg: #ffffff;

  --theme-button-text: #a8a9ab;
  --theme-button-code-hover-text: #0ba5ec;
  --theme-button-code-hover-bg: #e8f7fe;
  --theme-button-disable-hover-text: #854708;
  --theme-button-disable-hover-bg: #fef7e6;
  --theme-button-delete-hover-text: #b42318;
  --theme-button-delete-hover-bg: #fee4e2;
}

[data-theme="light"] .text-white {
  color: var(--theme-text-primary);
}

[data-theme="light"] .text-description,
[data-theme="light"] .text-white\/60 {
  color: var(--theme-text-secondary);
}

[data-theme="light"] .bg-theme-bg-secondary {
  border: 1px solid var(--theme-sidebar-border);
}

[data-theme="light"] .border-white\/10 {
  border-color: var(--theme-sidebar-border);
}

/*
This is to override the default border color for the select and input elements
in the onboarding flow when the theme is not light. This only applies to the
onboarding flow since its background is dark and is the same fill as the inputs.
*/
[data-layout="onboarding"] > * select:not([data-theme="light"]),
[data-layout="onboarding"] > * input:not([data-theme="light"]),
[data-layout="onboarding"] > * textarea:not([data-theme="light"]) {
  border: 1px solid #ffffff;
}

html,
body {
  padding: 0;
  margin: 0;
  font-family:
    "plus-jakarta-sans",
    -apple-system,
    BlinkMacSystemFont,
    Segoe UI,
    Roboto,
    Oxygen,
    Ubuntu,
    Cantarell,
    Fira Sans,
    Droid Sans,
    Helvetica Neue,
    sans-serif;
  background-color: white;
}

a {
  color: inherit;
  text-decoration: none;
}

* {
  box-sizing: border-box;
}

.g327 {
  border-color: #302f30;
}

@font-face {
  font-family: "plus-jakarta-sans";
  src: url("../public/fonts/PlusJakartaSans.ttf");
  font-display: swap;
}

.grr {
  grid-template-columns: repeat(2, 1fr);
}

.greyC {
  filter: gray;
  -webkit-filter: grayscale(100%);
  transition: 0.4s;
}

.greyC:hover {
  filter: none;
  -webkit-filter: none;
  transition: 0.4s;
}

.chat__message {
  transform-origin: 0 100%;
  transform: scale(0);
  animation: message 0.15s ease-out 0s forwards;
  animation-delay: 500ms;
}

@keyframes message {
  0% {
    max-height: 100%;
  }

  80% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
    max-height: 100%;
    overflow: visible;
    padding-top: 1rem;
  }
}

.doc__source {
  opacity: 0;
  animation-delay: 50ms;
  animation: citationAnimation 0.15s ease-out 0s forwards;
}

@keyframes citationAnimation {
  0% {
    opacity: 0;
  }

  80% {
    opacity: 0.5;
  }

  100% {
    opacity: 1;
  }
}

@media (prefers-color-scheme: light) {
  .sidebar-items:after {
    content: " ";
    position: absolute;
    left: 0;
    right: 0px;
    height: 4em;
    top: 69vh;
    z-index: 1;
    pointer-events: none;
  }
}

@media (prefers-color-scheme: dark) {
  .sidebar-items:after {
    content: " ";
    position: absolute;
    left: 0;
    right: 0px;
    height: 4em;
    top: 69vh;
    z-index: 1;
    pointer-events: none;
  }
}

@media (prefers-color-scheme: light) {
  .fade-up-border {
    background: linear-gradient(
      to bottom,
      rgba(220, 221, 223, 10%),
      rgb(220, 221, 223) 89%
    );
  }
}

@media (prefers-color-scheme: dark) {
  .fade-up-border {
    background: linear-gradient(
      to bottom,
      rgba(41, 37, 36, 50%),
      rgb(41 37 36) 90%
    );
  }
}

/**
 * ==============================================
 * Dot Falling
 * ==============================================
 */
.dot-falling {
  position: relative;
  left: -9999px;
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #eeeeee;
  color: #5fa4fa;
  box-shadow: 9999px 0 0 0 #eeeeee;
  animation: dot-falling 1.5s infinite linear;
  animation-delay: 0.1s;
}

.dot-falling::before,
.dot-falling::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
}

.dot-falling::before {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #eeeeee;
  color: #eeeeee;
  animation: dot-falling-before 1.5s infinite linear;
  animation-delay: 0s;
}

.dot-falling::after {
  width: 10px;
  height: 10px;
  border-radius: 5px;
  background-color: #eeeeee;
  color: #eeeeee;
  animation: dot-falling-after 1.5s infinite linear;
  animation-delay: 0.2s;
}

@keyframes dot-falling {
  0% {
    box-shadow: 9999px -15px 0 0 rgba(152, 128, 255, 0);
  }

  25%,
  50%,
  75% {
    box-shadow: 9999px 0 0 0 #eeeeee;
  }

  100% {
    box-shadow: 9999px 15px 0 0 rgba(152, 128, 255, 0);
  }
}

@keyframes dot-falling-before {
  0% {
    box-shadow: 9984px -15px 0 0 rgba(152, 128, 255, 0);
  }

  25%,
  50%,
  75% {
    box-shadow: 9984px 0 0 0 #eeeeee;
  }

  100% {
    box-shadow: 9984px 15px 0 0 rgba(152, 128, 255, 0);
  }
}

@keyframes dot-falling-after {
  0% {
    box-shadow: 10014px -15px 0 0 rgba(152, 128, 255, 0);
  }

  25%,
  50%,
  75% {
    box-shadow: 10014px 0 0 0 #eeeeee;
  }

  100% {
    box-shadow: 10014px 15px 0 0 rgba(152, 128, 255, 0);
  }
}

.show-scrollbar {
  overflow-y: scroll !important;
  scrollbar-width: thin !important;
  scrollbar-color: rgba(255, 255, 255, 0.3) rgba(0, 0, 0, 0.1) !important;
  -webkit-overflow-scrolling: touch !important;
}

.show-scrollbar::-webkit-scrollbar {
  width: 8px !important;
  display: block !important;
  background: transparent !important;
}

.show-scrollbar::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1) !important;
  margin: 3px !important;
  border-radius: 4px !important;
}

.show-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(255, 255, 255, 0.3) !important;
  border-radius: 4px !important;
  border: none !important;
  min-height: 40px !important;
}

.show-scrollbar::-webkit-scrollbar,
.show-scrollbar::-webkit-scrollbar-thumb,
.show-scrollbar::-webkit-scrollbar-track {
  visibility: visible !important;
  opacity: 1 !important;
}

.show-scrollbar,
.show-scrollbar::-webkit-scrollbar,
.show-scrollbar::-webkit-scrollbar-thumb,
.show-scrollbar::-webkit-scrollbar-track {
  transition: none !important;
  animation: none !important;
}

#chat-container::-webkit-scrollbar,
.no-scroll::-webkit-scrollbar {
  display: none !important;
}

/* Hide scrollbar for IE, Edge and Firefox */
.no-scroll {
  -ms-overflow-style: none !important;
  /* IE and Edge */
  scrollbar-width: none !important;
  /* Firefox */
}

.z-99 {
  z-index: 99;
}

.z-98 {
  z-index: 98;
}

.file-uploader {
  width: 100% !important;
  height: 100px !important;
}

.grid-loader > circle {
  fill: #008eff;
}

dialog {
  pointer-events: none;
  opacity: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

dialog[open] {
  opacity: 1;
  pointer-events: inherit;
}

dialog::backdrop {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.backdrop {
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(2px);
}

.animate-slow-pulse {
  transform: scale(1);
  animation: subtlePulse 20s infinite;
  will-change: transform;
}

@keyframes subtlePulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }

  100% {
    transform: scale(1);
  }
}

@keyframes subtleShift {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

.login-input-gradient {
  background: linear-gradient(
    180deg,
    rgba(61, 65, 71, 0.3) 0%,
    rgba(44, 47, 53, 0.3) 100%
  ) !important;
  box-shadow: 0px 4px 30px rgba(0, 0, 0, 0.25);
}

.white-fill {
  fill: white;
}

.tip:before {
  content: "";
  display: block;
  width: 0;
  height: 0;
  position: absolute;

  border-bottom: 8px solid transparent;
  border-top: 8px solid rgba(255, 255, 255, 0.5);
  border-left: 8px solid transparent;
  border-right: 8px solid transparent;
  border-radius: 0px 0px 0px 5px;
  left: 1%;

  top: 100%;
}

.user-reply > div:first-of-type {
  border: 2px solid white;
}

.reply > *:last-child::after {
  content: "|";
  animation: blink 1.5s steps(1) infinite;
  color: white;
  font-size: 14px;
}

@keyframes blink {
  0% {
    opacity: 0;
  }

  50% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@layer components {
  .radio-container:has(input:checked) {
    @apply border-blue-500 bg-blue-400/10 text-blue-800;
  }
}

.tooltip {
  @apply !bg-black !text-white !py-2 !px-3 !rounded-md;
}

.Toastify__toast-body {
  white-space: pre-line;
}

@keyframes slideDown {
  from {
    max-height: 0;
    opacity: 0;
  }

  to {
    max-height: 400px;
    opacity: 1;
  }
}

.slide-down {
  animation: slideDown 0.3s ease-out forwards;
}

.input-label {
  @apply text-[14px] font-bold text-white;
}

/**
 * ==============================================
 * Markdown Styles
 * ==============================================
 */
.markdown,
.markdown > * {
  font-weight: 400;
}

.markdown h1 {
  font-size: xx-large;
  line-height: 1.7;
  padding-left: 0.3rem;
}

.markdown h2 {
  line-height: 1.5;
  font-size: x-large;
  padding-left: 0.3rem;
}

.markdown h3 {
  line-height: 1.4;
  font-size: large;
  padding-left: 0.3rem;
}

/* Table Styles */

.markdown table {
  border-collapse: separate;
}

.markdown th {
  border-top: none;
}

.markdown td:first-child,
.markdown th:first-child {
  border-left: none;
}

.markdown table {
  width: 100%;
  border-collapse: collapse;
  color: #bdbdbe;
  font-size: 13px;
  margin: 30px 0px;
  border-radius: 10px;
  overflow: hidden;
  font-weight: normal;
}

.markdown table thead {
  color: #fff;
  text-transform: uppercase;
  font-weight: bolder;
}

.markdown hr {
  border: 0;
  border-top: 1px solid #cdcdcd40;
  margin: 1rem 0;
}

.markdown table th,
.markdown table td {
  padding: 8px 15px;
  border-bottom: 1px solid #cdcdcd2e;
  text-align: left;
}

.markdown table th {
  padding: 14px 15px;
}

.markdown > * a {
  color: var(--theme-button-cta);
  text-decoration: underline;
}

@media (max-width: 600px) {
  .markdown table th,
  .markdown table td {
    padding: 10px;
  }
}

[data-theme="light"] .markdown table,
[data-theme="light"] .markdown table th,
[data-theme="light"] .markdown table td {
  color: #000;
}

/* List Styles */
.markdown ol {
  list-style: decimal-leading-zero;
  padding-left: 0px;
  padding-top: 10px;
  margin: 10px;
}

.markdown ol li {
  margin-left: 20px;
  padding-left: 10px;
  position: relative;
  transition: all 0.3s ease;
  line-height: 1.4rem;
}

.markdown ol li::marker {
  padding-top: 10px;
}

.markdown ol li p {
  margin: 0.5rem;
  padding-top: 10px;
}

.markdown ol li a {
  text-decoration: underline;
}

.markdown ol li p a {
  text-decoration: underline;
}

.markdown ul {
  list-style: revert-layer;
  /* color: #cfcfcfcf; */
  padding-left: 0px;
  padding-top: 10px;
  padding-bottom: 10px;
  margin: 10px;
}

.markdown ul li::marker {
  color: #d0d0d0cf;
  padding-top: 10px;
}

.markdownul li {
  margin-left: 20px;

  padding-left: 10px;
  transition: all 0.3s ease;
  line-height: 1.4rem;
}

.markdownul li a {
  text-decoration: underline;
}

.markdown ul li > ul {
  padding-left: 20px;
  margin: 0px;
}

.markdown p {
  font-weight: 400;
  margin: 0.35rem;
}

.markdown > p > a,
.markdown p a {
  text-decoration: underline;
}

.markdown {
  text-wrap: wrap;
}

.markdown pre {
  margin: 20px 0;
}

.markdown strong {
  font-weight: 600;
  color: #fff;
}

.file-row {
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
}

.file-row:nth-child(even) {
  @apply bg-theme-bg-primary;
  background-color: var(--theme-file-row-even);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.file-row:nth-child(odd) {
  @apply bg-theme-bg-secondary;
  background-color: var(--theme-file-row-odd);
  border-bottom: 1px solid rgba(255, 255, 255, 0.05);
}

.file-row.selected:nth-child(even),
.file-row.selected:nth-child(odd) {
  background-color: var(--theme-file-row-selected-even);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

[data-theme="light"] .file-row.selected:nth-child(even),
[data-theme="light"] .file-row.selected:nth-child(odd) {
  border-bottom: 1px solid rgba(222, 222, 222, 0.5);
}

/* Flex upload modal to be a column when on small screens so that the UI
does not extend the close button beyond the viewport. */
@media (max-width: 1330px) {
  .upload-modal {
    @apply !flex-col !items-center !py-4 no-scroll;
  }

  .upload-modal-arrow {
    margin-top: 0px !important;
  }
}

.upload-modal {
  @apply flex-row items-start gap-x-6 justify-center;
}

.upload-modal-arrow {
  margin-top: 25%;
}

/* Scrollbar container */
.white-scrollbar {
  overflow-y: scroll;
  scrollbar-width: thin;
  scrollbar-color: #ffffff #18181b;
  margin-right: 8px;
}

/* Webkit browsers (Chrome, Safari) */
.white-scrollbar::-webkit-scrollbar {
  width: 3px;
  background-color: #18181b;
}

.white-scrollbar::-webkit-scrollbar-track {
  background-color: #18181b;
  margin-right: 8px;
}

.white-scrollbar::-webkit-scrollbar-thumb {
  background-color: #ffffff;
  border-radius: 4px;
  border: 2px solid #18181b;
}

.white-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #cccccc;
}

/* Recharts rendering styles */
.recharts-text > * {
  fill: #fff;
}

[data-theme="light"] .recharts-text > * {
  fill: #000;
}

.recharts-legend-wrapper {
  margin-bottom: 10px;
}

.text-tremor-content {
  padding-bottom: 10px;
}

.file-upload {
  -webkit-animation: fadein 0.3s linear forwards;
  animation: fadein 0.3s linear forwards;
}

.file-upload-fadeout {
  -webkit-animation: fadeout 0.3s linear forwards;
  animation: fadeout 0.3s linear forwards;
}

@-webkit-keyframes fadein {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@keyframes fadein {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

@-webkit-keyframes fadeout {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

@keyframes fadeout {
  0% {
    opacity: 1;
  }

  100% {
    opacity: 0;
  }
}

.search-input::-webkit-search-cancel-button {
  filter: grayscale(100%) invert(1) brightness(100) opacity(0.5);
}

.animate-remove {
  animation: fadeAndShrink 800ms forwards;
}

@keyframes fadeAndShrink {
  50% {
    opacity: 25%;
  }

  75% {
    opacity: 10%;
  }

  100% {
    height: 0px;
    opacity: 0%;
    display: none;
  }
}

/* Math/Katex formatting to prevent duplication of content on screen */
.katex-html[aria-hidden="true"] {
  display: none;
}

.katex-mathml {
  font-size: 20px;
}

.rti--container {
  @apply !bg-theme-settings-input-bg !text-white !placeholder-white !placeholder-opacity-60 !text-sm !rounded-lg !p-2.5;
}

@keyframes fadeUpIn {
  0% {
    opacity: 0;
    transform: translateY(5px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fadeUpIn {
  animation: fadeUpIn 0.3s ease-out forwards;
}

@keyframes bounce-subtle {
  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-2px);
  }
}

@keyframes thoughtTransition {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }

  30% {
    opacity: 1;
    transform: translateY(0);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-thoughtTransition {
  animation: thoughtTransition 0.5s ease-out forwards;
}

.checklist-completed {
  -webkit-animation: fadein 0.3s linear forwards;
  animation: fadein 0.3s linear forwards;
}
