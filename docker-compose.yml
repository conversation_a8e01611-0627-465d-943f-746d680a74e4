version: '3.8'

# AI Development Assistant - Unified Docker Compose
# ================================================
# This file unifies all services with organized ports:
# - AnythingLLM: 3001
# - Ollama WebUI: 3000  
# - n8n: 5678
# - Ollama API: 11434

networks:
  ai-dev-network:
    driver: bridge
    name: ai-dev-network

volumes:
  # AnythingLLM volumes
  anythingllm_storage:
    driver: local
  anythingllm_hotdir:
    driver: local
  anythingllm_outputs:
    driver: local
  
  # n8n volumes
  n8n_data:
    driver: local
    
  # Ollama volumes
  ollama_data:
    driver: local
  ollama_webui_data:
    driver: local

services:
  # ==========================================
  # AnythingLLM - Knowledge Management System
  # ==========================================
  anythingllm:
    container_name: anythingllm
    image: mintplexlabs/anythingllm:latest
    restart: unless-stopped
    cap_add:
      - SYS_ADMIN
    ports:
      - "3001:3001"
    volumes:
      - anythingllm_storage:/app/server/storage
      - anythingllm_hotdir:/app/collector/hotdir
      - anythingllm_outputs:/app/collector/outputs
    environment:
      # LLM Configuration
      - LLM_PROVIDER=${LLM_PROVIDER:-ollama}
      - OLLAMA_BASE_PATH=${OLLAMA_BASE_PATH:-http://ollama:11434}
      - OLLAMA_MODEL_PREF=${OLLAMA_MODEL_PREF:-llama3:8b}
      - OLLAMA_MODEL_TOKEN_LIMIT=${OLLAMA_MODEL_TOKEN_LIMIT:-4096}
      - OLLAMA_PERFORMANCE_MODE=${OLLAMA_PERFORMANCE_MODE:-base}
      - OLLAMA_KEEP_ALIVE_TIMEOUT=${OLLAMA_KEEP_ALIVE_TIMEOUT:-300}
      
      # Database Configuration
      - EMBEDDING_ENGINE=${EMBEDDING_ENGINE:-native}
      - VECTOR_DB=${VECTOR_DB:-lancedb}
      
      # Security Configuration
      - AUTH_TOKEN=${AUTH_TOKEN}
      - JWT_SECRET=${JWT_SECRET}
      - SIG_KEY=${SIG_KEY}
      - SIG_SALT=${SIG_SALT}
      
      # Server Configuration
      - STORAGE_DIR=/app/server/storage
      - SERVER_PORT=3001
    networks:
      - ai-dev-network
    depends_on:
      - ollama
    extra_hosts:
      - "host.docker.internal:host-gateway"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/v1/system/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # ==========================================
  # Ollama - Local LLM Server
  # ==========================================
  ollama:
    container_name: ollama
    image: ollama/ollama:latest
    restart: unless-stopped
    ports:
      - "11434:11434"
    volumes:
      - ollama_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0:11434
    networks:
      - ai-dev-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:11434/api/tags"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    # GPU support (uncomment if you have NVIDIA GPU)
    # deploy:
    #   resources:
    #     reservations:
    #       devices:
    #         - driver: nvidia
    #           count: 1
    #           capabilities: [gpu]

  # ==========================================
  # Ollama WebUI - User Interface for Ollama
  # ==========================================
  ollama-webui:
    container_name: ollama-webui
    image: ghcr.io/open-webui/open-webui:main
    restart: unless-stopped
    ports:
      - "3000:8080"
    environment:
      - OLLAMA_BASE_URL=http://ollama:11434
      - WEBUI_SECRET_KEY=${WEBUI_SECRET_KEY:-your-secret-key-here}
    volumes:
      - ollama_webui_data:/app/backend/data
    networks:
      - ai-dev-network
    depends_on:
      - ollama
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ==========================================
  # AI Coordinator - Smart Coordination Layer
  # ==========================================
  ai-coordinator:
    build: ./model_mix/ai-coordinator
    container_name: ai-coordinator
    restart: unless-stopped
    ports:
      - "3333:3333"
    environment:
      # Server Configuration
      - PORT=3333
      - NODE_ENV=${NODE_ENV:-production}

      # API Keys
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}

      # External Services URLs
      - OLLAMA_URL=http://ollama:11434
      - N8N_URL=http://n8n:5678
      - ANYTHINGLLM_URL=http://anythingllm:3001

      # Webhook Configuration
      - N8N_WEBHOOK_URL=http://n8n:5678/webhook/ai-coordinator

      # Debug Mode
      - DEBUG=${DEBUG:-false}
    volumes:
      - ./model_mix/ai-coordinator/logs:/app/logs
      - ./model_mix/ai-coordinator/config:/app/config
    networks:
      - ai-dev-network
    depends_on:
      - ollama
      - anythingllm
      - n8n
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # ==========================================
  # n8n - Workflow Automation
  # ==========================================
  n8n:
    container_name: n8n
    image: n8nio/n8n:latest
    restart: unless-stopped
    ports:
      - "5678:5678"
    environment:
      # Basic Authentication
      - N8N_BASIC_AUTH_ACTIVE=${N8N_BASIC_AUTH_ACTIVE:-true}
      - N8N_BASIC_AUTH_USER=${N8N_BASIC_AUTH_USER:-admin}
      - N8N_BASIC_AUTH_PASSWORD=${N8N_BASIC_AUTH_PASSWORD:-password}

      # Database Configuration
      - DB_TYPE=${DB_TYPE:-sqlite}
      - DB_SQLITE_DATABASE=/home/<USER>/.n8n/database.sqlite

      # Server Configuration
      - N8N_HOST=${N8N_HOST:-0.0.0.0}
      - N8N_PORT=${N8N_PORT:-5678}
      - N8N_PROTOCOL=http
      - WEBHOOK_URL=${WEBHOOK_URL:-http://localhost:5678}

      # Security Settings
      - N8N_SECURE_COOKIE=false
      - N8N_ENCRYPTION_KEY=${N8N_ENCRYPTION_KEY}

      # Timezone
      - GENERIC_TIMEZONE=${GENERIC_TIMEZONE:-UTC}
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - ai-dev-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:5678/healthz"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
