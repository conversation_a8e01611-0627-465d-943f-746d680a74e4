version: '3.8'

services:
  ai-coordinator:
    build: .
    container_name: ai-coordinator
    restart: unless-stopped
    ports:
      - "3333:3333"
    environment:
      - PORT=3333
      - NODE_ENV=production
      - GEMINI_API_KEY=${GEMINI_API_KEY:-your-gemini-api-key-here}
      - OLLAMA_URL=http://host.docker.internal:11434
      - DEBUG=true
    volumes:
      - ./logs:/app/logs
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
