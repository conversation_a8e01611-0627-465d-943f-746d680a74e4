version: '3.8'

services:
  ai-coordinator:
    build: .
    container_name: ai-coordinator
    restart: unless-stopped
    ports:
      - "3333:3333"
    environment:
      # Server Configuration
      - PORT=3333
      - NODE_ENV=production
      
      # API Keys
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      
      # External Services URLs
      - OLLAMA_URL=http://host.docker.internal:11434
      - N8N_URL=http://host.docker.internal:5678
      - ANYTHINGLLM_URL=http://host.docker.internal:3001
      
      # Webhook Configuration
      - N8N_WEBHOOK_URL=http://host.docker.internal:5678/webhook/ai-coordinator
      
      # Debug Mode
      - DEBUG=${DEBUG:-false}
      
    volumes:
      # Mount logs directory
      - ./logs:/app/logs
      
      # Mount config directory
      - ./config:/app/config
      
    networks:
      - ai-network
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3333/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # Depends on external services (optional)
    # depends_on:
    #   - ollama
    #   - n8n

  # Optional: Redis for caching and message queue
  redis:
    image: redis:7-alpine
    container_name: ai-coordinator-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - ai-network
    command: redis-server --appendonly yes

  # Optional: PostgreSQL for data storage
  postgres:
    image: postgres:15-alpine
    container_name: ai-coordinator-db
    restart: unless-stopped
    environment:
      - POSTGRES_DB=ai_coordinator
      - POSTGRES_USER=ai_user
      - POSTGRES_PASSWORD=${DB_PASSWORD:-ai_password}
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - ai-network

volumes:
  redis_data:
    driver: local
  postgres_data:
    driver: local

networks:
  ai-network:
    driver: bridge
    external: false
