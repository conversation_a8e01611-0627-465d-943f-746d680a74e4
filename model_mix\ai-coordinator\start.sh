#!/bin/bash

echo "🚀 Starting AI Coordinator..."

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js first."
    exit 1
fi

# Check if npm is installed
if ! command -v npm &> /dev/null; then
    echo "❌ npm is not installed. Please install npm first."
    exit 1
fi

# Install dependencies if node_modules doesn't exist
if [ ! -d "node_modules" ]; then
    echo "📦 Installing dependencies..."
    npm install
fi

# Check if .env file exists
if [ ! -f ".env" ]; then
    echo "⚠️  .env file not found. Please configure your API keys."
    echo "📝 Copy .env.example to .env and fill in your keys"
fi

# Start the server
echo "🌟 Starting AI Coordinator on port 3333..."
echo "📊 Health check will be available at: http://localhost:3333/api/health"
echo "🧪 Test endpoint: http://localhost:3333/api/test"
echo ""

npm start
