const axios = require('axios');

const BASE_URL = 'http://localhost:3333';

async function testCoordinator() {
  console.log('🧪 Testing AI Coordinator...\n');
  
  try {
    // Test 1: Health Check
    console.log('1️⃣ Testing Health Check...');
    const healthResponse = await axios.get(`${BASE_URL}/api/health`);
    console.log('✅ Health:', healthResponse.data);
    console.log('');
    
    // Test 2: Simple Coordination
    console.log('2️⃣ Testing Simple Coordination...');
    const simpleResponse = await axios.post(`${BASE_URL}/api/coordinate`, {
      prompt: 'قل مرحبا باللغة العربية',
      options: { priority: 'fast' }
    });
    console.log('✅ Simple Response:', simpleResponse.data);
    console.log('');
    
    // Test 3: Complex Task
    console.log('3️⃣ Testing Complex Task...');
    const complexResponse = await axios.post(`${BASE_URL}/api/coordinate`, {
      prompt: 'Analyze the benefits of microservices architecture',
      options: { complexity: 'high' }
    });
    console.log('✅ Complex Response:', complexResponse.data);
    console.log('');
    
    // Test 4: Collaborative Response
    console.log('4️⃣ Testing Collaborative Response...');
    const collaborativeResponse = await axios.post(`${BASE_URL}/api/collaborate`, {
      prompt: 'What is artificial intelligence?'
    });
    console.log('✅ Collaborative Response:', collaborativeResponse.data);
    console.log('');
    
    console.log('🎉 All tests completed successfully!');
    
  } catch (error) {
    console.error('❌ Test failed:', error.response?.data || error.message);
  }
}

// Test different scenarios
async function testScenarios() {
  console.log('🎯 Testing Different Scenarios...\n');
  
  const scenarios = [
    {
      name: 'Quick Question',
      prompt: 'What is 2+2?',
      options: { priority: 'fast' }
    },
    {
      name: 'Code Help',
      prompt: 'How to create a React component?',
      context: 'code development',
      options: { complexity: 'medium' }
    },
    {
      name: 'Research Task',
      prompt: 'Research the latest trends in AI development',
      options: { complexity: 'high' }
    },
    {
      name: 'Arabic Question',
      prompt: 'ما هو الذكاء الاصطناعي؟',
      options: { priority: 'normal' }
    }
  ];
  
  for (const scenario of scenarios) {
    try {
      console.log(`📝 Testing: ${scenario.name}`);
      const response = await axios.post(`${BASE_URL}/api/coordinate`, scenario);
      console.log(`✅ Model chosen: ${response.data.decision?.model}`);
      console.log(`📄 Response: ${response.data.response?.substring(0, 100)}...`);
      console.log('');
    } catch (error) {
      console.error(`❌ ${scenario.name} failed:`, error.message);
    }
  }
}

// Run tests
if (require.main === module) {
  testCoordinator()
    .then(() => testScenarios())
    .catch(console.error);
}

module.exports = { testCoordinator, testScenarios };
