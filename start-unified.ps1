# AI Development Assistant - Unified Startup Script (PowerShell)
# ==============================================================
# This script starts all services using the unified docker-compose.yml

Write-Host "🚀 AI Development Assistant - Unified Startup" -ForegroundColor Cyan
Write-Host "==============================================" -ForegroundColor Cyan
Write-Host ""

# Function to print colored output
function Write-Status {
    param($Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param($Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param($Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param($Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

# Check if Docker is running
Write-Status "Checking Docker status..."
try {
    docker info | Out-Null
    Write-Success "Docker is running"
} catch {
    Write-Error "Docker is not running. Please start Docker Desktop first."
    exit 1
}

# Check if docker-compose.yml exists
if (-not (Test-Path "docker-compose.yml")) {
    Write-Error "docker-compose.yml not found in current directory"
    exit 1
}

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Warning ".env file not found. Using default values."
}

# Stop any existing containers
Write-Status "Stopping any existing containers..."
docker-compose down

# Pull latest images
Write-Status "Pulling latest Docker images..."
docker-compose pull

# Start services
Write-Status "Starting all services..."
docker-compose up -d

# Wait a moment for services to start
Write-Status "Waiting for services to initialize..."
Start-Sleep -Seconds 10

# Check service status
Write-Status "Checking service status..."
Write-Host ""

# Function to check if a service is responding
function Test-Service {
    param(
        [string]$ServiceName,
        [string]$Port,
        [string]$Url
    )

    try {
        $response = Invoke-WebRequest -Uri $Url -Method Get -TimeoutSec 5 -UseBasicParsing
        if ($response.StatusCode -eq 200 -or $response.StatusCode -eq 302 -or $response.StatusCode -eq 404) {
            Write-Success "$ServiceName is running on port $Port"
            Write-Host "           URL: $Url" -ForegroundColor Gray
        }
    } catch {
        Write-Warning "$ServiceName may still be starting on port $Port"
        Write-Host "           URL: $Url" -ForegroundColor Gray
    }
}

# Check each service
Write-Host "📊 Service Status:" -ForegroundColor Cyan
Write-Host "=================="
Test-Service "AI Coordinator" "3333" "http://localhost:3333/api/health"
Test-Service "AnythingLLM" "3001" "http://localhost:3001"
Test-Service "Ollama WebUI" "3000" "http://localhost:3000"
Test-Service "n8n" "5678" "http://localhost:5678"
Test-Service "Ollama API" "11434" "http://localhost:11434/api/tags"

Write-Host ""
Write-Host "🔗 Quick Access URLs:" -ForegroundColor Cyan
Write-Host "===================="
Write-Host "• 🧠 AI Coordinator: http://localhost:3333  (المشروع الرئيسي)"
Write-Host "• 💬 AnythingLLM:    http://localhost:3001"
Write-Host "• 🖥️ Ollama WebUI:   http://localhost:3000"
Write-Host "• 🔄 n8n:            http://localhost:5678"
Write-Host "• 🤖 Ollama API:     http://localhost:11434"

Write-Host ""
Write-Host "📋 Default Credentials:" -ForegroundColor Cyan
Write-Host "======================"
Write-Host "• n8n:          admin / password123"
Write-Host "• AnythingLLM:  (configured in .env)"

Write-Host ""
Write-Host "🛠️ Useful Commands:" -ForegroundColor Cyan
Write-Host "=================="
Write-Host "• View logs:        docker-compose logs -f [service_name]"
Write-Host "• Stop all:         docker-compose down"
Write-Host "• Restart service:  docker-compose restart [service_name]"
Write-Host "• View status:      docker-compose ps"

Write-Host ""
Write-Success "All services started successfully!"
Write-Status "Check the URLs above to access your services."

# Optional: Open browser (uncomment if desired)
# Write-Status "Opening AnythingLLM in browser..."
# Start-Process "http://localhost:3001"
